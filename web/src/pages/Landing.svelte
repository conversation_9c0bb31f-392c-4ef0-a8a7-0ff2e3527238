<script module>
  import { <PERSON> } from "@inertiajs/svelte";
  import AuthLayout from "../layouts/AuthLayout.svelte";
  export const layout = AuthLayout;
</script>

<script lang="ts">
  import { fade, slide, fly, scale } from "svelte/transition";
  import { elasticOut, quintOut } from "svelte/easing";
  import { Button } from "$lib/components/ui/button";
  import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "$lib/components/ui/card";
  import { onMount } from "svelte";

  let mounted = false;
  onMount(() => {
    mounted = true;
  });

  // Animation delays for staggered effects
  const delays = [0, 200, 400, 600];
</script>

<svelte:head>
  <title>DnD - Dental & Diagnosis Platform</title>
  <meta name="description" content="Modern dental clinic management platform that reduces administrative time by 20 hours per week and increases collections by 29%." />
</svelte:head>

<main class="min-h-screen bg-gradient-to-br from-background via-background to-muted">
  <!-- Hero Section with Waving Background -->
  <section class="relative overflow-hidden">
    <!-- Animated Waving Background -->
    <div class="absolute inset-0 opacity-20 overflow-hidden">
      <svg class="absolute bottom-0 left-0 w-full h-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 800" preserveAspectRatio="none">
        <path 
          d="M-200,400 Q100,200 400,400 T1000,400 T1600,400 V800 H-200 Z" 
          fill="url(#waveGradient)" 
          class="animate-pulse"
        >
          <animateTransform 
            attributeName="transform" 
            attributeType="XML" 
            type="translate" 
            values="0 0; 200 0; 0 0" 
            dur="8s" 
            repeatCount="indefinite"
          />
        </path>
        <path 
          d="M-200,450 Q200,250 600,450 T1200,450 T1600,450 V800 H-200 Z" 
          fill="url(#waveGradient2)" 
          opacity="0.7"
        >
          <animateTransform 
            attributeName="transform" 
            attributeType="XML" 
            type="translate" 
            values="0 0; -200 0; 0 0" 
            dur="12s" 
            repeatCount="indefinite"
          />
        </path>
        <path 
          d="M-200,500 Q300,300 700,500 T1300,500 T1600,500 V800 H-200 Z" 
          fill="url(#waveGradient3)" 
          opacity="0.5"
        >
          <animateTransform 
            attributeName="transform" 
            attributeType="XML" 
            type="translate" 
            values="0 0; 150 0; 0 0" 
            dur="15s" 
            repeatCount="indefinite"
          />
        </path>
        <defs>
          <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:oklch(0.6 0.15 180);stop-opacity:1" />
            <stop offset="100%" style="stop-color:oklch(0.7 0.15 240);stop-opacity:1" />
          </linearGradient>
          <linearGradient id="waveGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:oklch(0.8 0.15 120);stop-opacity:1" />
            <stop offset="100%" style="stop-color:oklch(0.7 0.15 60);stop-opacity:1" />
          </linearGradient>
          <linearGradient id="waveGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:oklch(0.9 0.15 300);stop-opacity:1" />
            <stop offset="100%" style="stop-color:oklch(0.8 0.15 180);stop-opacity:1" />
          </linearGradient>
        </defs>
      </svg>
    </div>

    <div class="relative z-10 container mx-auto px-4 py-20">
      <div class="max-w-4xl mx-auto text-center">
        {#if mounted}
          <h1 
            class="text-6xl md:text-8xl font-bold mb-6 bg-gradient-to-r from-primary via-chart-1 to-chart-2 bg-clip-text text-transparent"
            in:fly={{ y: 50, duration: 1000, easing: elasticOut }}
          >
            DnD Platform
          </h1>
          
          <p 
            class="text-xl md:text-2xl text-muted-foreground mb-8 max-w-2xl mx-auto"
            in:fly={{ y: 30, duration: 1000, delay: 300, easing: quintOut }}
          >
            Dental & Diagnosis Clinic Management Platform
          </p>
          
          <p 
            class="text-lg text-foreground/80 mb-12 max-w-3xl mx-auto"
            in:fade={{ duration: 1000, delay: 600 }}
          >
            Transform your dental practice with our modern platform that reduces administrative time by 
            <span class="font-semibold text-chart-1">20 hours per week</span> and increases collections by 
            <span class="font-semibold text-chart-2">29%</span>
          </p>

          <div 
            class="flex flex-col sm:flex-row gap-4 justify-center items-center"
            in:fly={{ y: 20, duration: 800, delay: 900, easing: quintOut }}
          >
            <Button size="lg" class="text-lg px-8 py-6 animate-pulse hover:animate-none" disabled={false}>
              <Link href="/dashboard" class="flex items-center gap-2">
                Get Started
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
              </Link>
            </Button>
            
            <Button variant="outline" size="lg" class="text-lg px-8 py-6" disabled={false}>
              Learn More
            </Button>
          </div>
        {/if}
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section class="py-20 bg-card/50">
    <div class="container mx-auto px-4">
      <div class="max-w-6xl mx-auto">
        {#if mounted}
          <div class="text-center mb-16">
            <h2 
              class="text-4xl md:text-5xl font-bold mb-6"
              in:fly={{ y: 30, duration: 800, delay: 200 }}
            >
              Why Choose DnD?
            </h2>
            <p 
              class="text-xl text-muted-foreground max-w-2xl mx-auto"
              in:fade={{ duration: 800, delay: 400 }}
            >
              Built on the principle of "zero wasted clicks" - every feature is designed to save you time
            </p>
          </div>

          <div class="grid md:grid-cols-3 gap-8">
            {#each [
              {
                title: "⚡ Lightning Fast Charting",
                description: "Record new findings in ≤45 seconds with our interactive odontograms and smart defaults",
                metric: "3.5× faster than traditional charting"
              },
              {
                title: "💰 Boost Revenue",
                description: "Automated reminders and streamlined workflows help achieve 95% invoice payment within 30 days",
                metric: "29% increase in collections"
              },
              {
                title: "📈 Scale Seamlessly",
                description: "Cloud-native architecture that grows with your practice, tested up to 100k patients",
                metric: "Linear scaling performance"
              }
            ] as feature, i}
              <div in:fly={{ y: 50, duration: 800, delay: delays[i] }}>
                <Card class="h-full hover:shadow-lg transition-all duration-300 hover:scale-105">
                  <CardHeader class="">
                    <CardTitle class="text-2xl">{feature.title}</CardTitle>
                    <CardDescription class="text-base">{feature.description}</CardDescription>
                  </CardHeader>
                  <CardContent class="">
                    <div class="text-sm font-semibold text-chart-1 bg-chart-1/10 rounded-full px-3 py-1 inline-block">
                      {feature.metric}
                    </div>
                  </CardContent>
                </Card>
              </div>
            {/each}
          </div>
        {/if}
      </div>
    </div>
  </section>

  <!-- Core Modules Section -->
  <section class="py-20">
    <div class="container mx-auto px-4">
      <div class="max-w-6xl mx-auto">
        {#if mounted}
          <div class="text-center mb-16">
            <h2 
              class="text-4xl md:text-5xl font-bold mb-6"
              in:fly={{ y: 30, duration: 800, delay: 100 }}
            >
              Complete Practice Management
            </h2>
            <p 
              class="text-xl text-muted-foreground max-w-2xl mx-auto"
              in:fade={{ duration: 800, delay: 300 }}
            >
              Five integrated modules that work together seamlessly
            </p>
          </div>

          <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {#each [
              {
                title: "Patient Management",
                description: "Unified profiles, advanced search, medical alerts, and automated follow-ups",
                icon: "👥"
              },
              {
                title: "Clinical Charting",
                description: "Interactive odontograms, FDI charts, drag-and-drop file uploads, DICOM support",
                icon: "🦷"
              },
              {
                title: "Diagnosis & Prognosis",
                description: "250+ ICD-11 dental codes, AI suggestions, and evidence-based prognosis scales",
                icon: "📋"
              },
              {
                title: "Invoicing & Financials",
                description: "One-click invoicing, payment tracking, loyalty programs, and financial reports",
                icon: "💳"
              },
              {
                title: "Workflow & Notifications",
                description: "Role-based dashboards, automated reminders, task management, and team collaboration",
                icon: "🔔"
              }
            ] as module, i}
              <div in:scale={{ duration: 600, delay: i * 100 }}>
                <Card class="hover:shadow-lg transition-all duration-300 hover:scale-105">
                  <CardHeader class="">
                    <CardTitle class="text-xl flex items-center gap-2">
                      <span class="text-2xl">{module.icon}</span>
                      {module.title}
                    </CardTitle>
                    <CardDescription class="">{module.description}</CardDescription>
                  </CardHeader>
                </Card>
              </div>
            {/each}
          </div>
        {/if}
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 bg-gradient-to-r from-primary/10 via-chart-1/10 to-chart-2/10">
    <div class="container mx-auto px-4">
      <div class="max-w-4xl mx-auto text-center">
        {#if mounted}
          <h2 
            class="text-4xl md:text-5xl font-bold mb-6"
            in:fly={{ y: 30, duration: 800 }}
          >
            Ready to Transform Your Practice?
          </h2>
          
          <p 
            class="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto"
            in:fade={{ duration: 800, delay: 200 }}
          >
            Join modern dental practices that have already improved their efficiency and revenue
          </p>

          <div 
            class="flex flex-col sm:flex-row gap-4 justify-center items-center"
            in:fly={{ y: 20, duration: 800, delay: 400 }}
          >
            <Button size="lg" class="text-lg px-8 py-6 animate-bounce hover:animate-none" disabled={false}>
              <Link href="/dashboard" class="flex items-center gap-2">
                Access Dashboard
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </Link>
            </Button>
          </div>
        {/if}
      </div>
    </div>
  </section>
</main>

<style>
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
</style>
