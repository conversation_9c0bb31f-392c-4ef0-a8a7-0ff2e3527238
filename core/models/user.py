import auto_prefetch
from django.contrib.auth.models import Abstract<PERSON><PERSON>, UserManager
from django.core.validators import RegexValidator
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_lifecycle import BEFORE_CREATE, BEFORE_SAVE, hook

from abstract.models import IntEntity


class CustomUserManager(UserManager):
    """Custom manager for the User model."""
    
    def create_user(self, username=None, phone_number=None, password=None, **extra_fields):
        """Create and save a regular user."""
        if not username and not phone_number:
            raise ValueError(_('Either username or phone number must be provided'))
        
        # For patients, use phone_number as username if username is not provided
        if not username and phone_number:
            username = phone_number
        
        # Normalize email if provided
        if extra_fields.get('email'):
            extra_fields['email'] = self.normalize_email(extra_fields['email'])
        
        # Set phone_number in extra_fields if provided
        if phone_number:
            extra_fields['phone_number'] = phone_number
        
        user = self.model(username=username, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user
    
    def create_superuser(self, username, password=None, **extra_fields):
        """Create and save a superuser."""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('user_type', User.ADMIN)
        
        if extra_fields.get('is_staff') is not True:
            raise ValueError(_('Superuser must have is_staff=True.'))
        if extra_fields.get('is_superuser') is not True:
            raise ValueError(_('Superuser must have is_superuser=True.'))
        
        return self.create_user(username=username, password=password, **extra_fields)


class User(AbstractUser, IntEntity):
    """Custom user model that supports both username and phone authentication."""
    
    # User types
    ADMIN = 'admin'
    RECEPTIONIST = 'receptionist'
    PATIENT = 'patient'
    
    USER_TYPE_CHOICES = [
        (ADMIN, _('Admin')),
        (RECEPTIONIST, _('Receptionist')),
        (PATIENT, _('Patient')),
    ]
    
    # Make username optional (it can be null for patients using phone number)
    username = models.CharField(
        _('Username'),
        max_length=150,
        unique=True,
        null=True,
        blank=True,
        help_text=_('Required for Admin and Receptionist. 150 characters or fewer. Letters, digits and @/./+/-/_ only.'),
        validators=[
            RegexValidator(
                regex=r'^[\w.@+-]+$',
                message=_('Username may only contain letters, numbers, and @/./+/-/_ characters.')
            )
        ]
    )
    
    phone_number = models.CharField(
        _('Phone Number'),
        max_length=20,
        unique=True,
        null=True,
        blank=True,
        help_text=_('Required for Patient authentication'),
        validators=[
            RegexValidator(
                regex=r'^\+?1?\d{9,15}$',
                message=_('Phone number must be entered in the format: "+999999999". Up to 15 digits allowed.')
            )
        ]
    )
    

    
    # User type
    user_type = models.CharField(
        _('User Type'),
        max_length=20,
        choices=USER_TYPE_CHOICES,
        default=PATIENT
    )
    
    # Additional fields
    last_login_ip = models.GenericIPAddressField(_('Last Login IP'), null=True, blank=True)
    
    objects = CustomUserManager()
    
    USERNAME_FIELD = 'username'  # Default for admin/receptionist
    REQUIRED_FIELDS = ['email']
    
    class Meta(IntEntity.Meta):
        abstract = False
        verbose_name = _('User')
        verbose_name_plural = _('Users')
        default_permissions = {}
        indexes = [
            models.Index(fields=['username']),
            models.Index(fields=['phone_number']),
            models.Index(fields=['email']),
            models.Index(fields=['user_type']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        if self.user_type == self.PATIENT and self.phone_number:
            return f"{self.get_full_name()} ({self.phone_number})"
        elif self.username:
            return f"{self.get_full_name()} ({self.username})"
        else:
            return f"{self.get_full_name()} ({self.email})"
    
    def get_full_name(self):
        """Return the first_name plus the last_name, with a space in between."""
        full_name = f'{self.first_name} {self.last_name}'.strip()
        return full_name if full_name else self.username or self.phone_number or self.email
    
    def get_short_name(self):
        """Return the short name for the user."""
        return self.first_name or self.username or self.phone_number
    
    def clean(self):
        """Validate the user model."""
        super().clean()
        from django.core.exceptions import ValidationError
        
        # Validate authentication requirements based on user type
        if self.user_type in [self.ADMIN, self.RECEPTIONIST]:
            if not self.username:
                raise ValidationError({
                    'username': _('Username is required for Admin and Receptionist users.')
                })
        elif self.user_type == self.PATIENT:
            if not self.phone_number:
                raise ValidationError({
                    'phone_number': _('Phone number is required for Patient users.')
                })
    
    @hook(BEFORE_SAVE)
    def set_staff_status(self):
        """Set staff status based on user type using lifecycle hook."""
        if self.user_type == self.ADMIN:
            self.is_staff = True
        elif self.user_type == self.RECEPTIONIST:
            self.is_staff = True
        else:  # PATIENT
            self.is_staff = False

    @hook(BEFORE_CREATE)
    def set_username_for_patient(self):
        """Set username to phone_number for patients if username is not provided."""
        if self.user_type == self.PATIENT and not self.username and self.phone_number:
            self.username = self.phone_number


class AdminManager(CustomUserManager):
    """Manager for Admin users."""
    
    def get_queryset(self):
        return super().get_queryset().filter(user_type=User.ADMIN)
    
    def create_user(self, username, password=None, **extra_fields):
        extra_fields['user_type'] = User.ADMIN
        extra_fields.setdefault('is_staff', True)
        return super().create_user(username=username, password=password, **extra_fields)


class ReceptionistManager(CustomUserManager):
    """Manager for Receptionist users."""
    
    def get_queryset(self):
        return super().get_queryset().filter(user_type=User.RECEPTIONIST)
    
    def create_user(self, username, password=None, **extra_fields):
        extra_fields['user_type'] = User.RECEPTIONIST
        extra_fields.setdefault('is_staff', True)
        return super().create_user(username=username, password=password, **extra_fields)


class PatientUserManager(CustomUserManager):
    """Manager for Patient users."""
    
    def get_queryset(self):
        return super().get_queryset().filter(user_type=User.PATIENT)
    
    def create_user(self, phone_number, password=None, **extra_fields):
        extra_fields['user_type'] = User.PATIENT
        extra_fields.setdefault('is_staff', False)
        return super().create_user(phone_number=phone_number, password=password, **extra_fields)


class Admin(User):
    """Proxy model for Admin users."""
    
    objects = AdminManager()
    
    class Meta(User.Meta, auto_prefetch.Model.Meta):
        proxy = True
        verbose_name = _('Admin')
        verbose_name_plural = _('Admins')
        default_permissions = {}
    
    @hook(BEFORE_CREATE)
    def set_user_type(self):
        """Set user type to Admin using lifecycle hook."""
        self.user_type = User.ADMIN


class Receptionist(User):
    """Proxy model for Receptionist users."""
    
    objects = ReceptionistManager()
    
    class Meta(User.Meta, auto_prefetch.Model.Meta):
        proxy = True
        verbose_name = _('Receptionist')
        verbose_name_plural = _('Receptionists')
        default_permissions = {}

    @hook(BEFORE_CREATE)
    def set_user_type(self):
        """Set user type to Receptionist using lifecycle hook."""
        self.user_type = User.RECEPTIONIST


class PatientUser(User):
    """Proxy model for Patient users."""
    
    objects = PatientUserManager()
    
    class Meta(User.Meta, auto_prefetch.Model.Meta):
        proxy = True
        verbose_name = _('Patient User')
        verbose_name_plural = _('Patient Users')
        default_permissions = {}
        
    @hook(BEFORE_CREATE)
    def set_user_type(self):
        """Set user type to Patient using lifecycle hook."""
        self.user_type = User.PATIENT


class PhoneNumberBackend:
    """
    Custom authentication backend that allows patients to authenticate 
    using their phone number instead of username.
    """
    
    def authenticate(self, request, username=None, password=None, phone_number=None, **kwargs):
        """
        Authenticate user by phone number (for patients) or username (for admin/receptionist).
        """
        UserModel = User
        
        if phone_number:
            # Phone number authentication for patients
            try:
                user = UserModel.objects.get(phone_number=phone_number, user_type=UserModel.PATIENT)
                if user.check_password(password) and self.user_can_authenticate(user):
                    return user
            except UserModel.DoesNotExist:
                return None
        elif username:
            # Username authentication for admin/receptionist
            try:
                user = UserModel.objects.get(username=username, user_type__in=[UserModel.ADMIN, UserModel.RECEPTIONIST])
                if user.check_password(password) and self.user_can_authenticate(user):
                    return user
            except UserModel.DoesNotExist:
                return None
        
        return None
    
    def user_can_authenticate(self, user):
        """
        Reject users with is_active=False. Custom user models that don't have
        an `is_active` field are allowed.
        """
        return getattr(user, 'is_active', True)
    
    def get_user(self, user_id):
        """Retrieve user by ID."""
        UserModel = User
        try:
            return UserModel.objects.get(pk=user_id)
        except UserModel.DoesNotExist:
            return None
